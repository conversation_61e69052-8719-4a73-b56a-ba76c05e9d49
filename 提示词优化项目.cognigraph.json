{"project_info": {"name": "逻明AI编程工作流系统优化", "description": "基于用户与DeepSeek R1的讨论，重新设计AI编程工具的提示词系统，按照AI真实思考流程优化工作流程", "role": "提示词工程专家", "created_date": "2025-01-25", "last_updated": "2025-01-25", "version": "v0.002"}, "requirements": {"core_needs": ["按照AI工作的真实思考流程重新排序提示词", "优化提示词结构，提高Token效率", "引入CogniGraph™概念作为核心记忆机制", "建立信息收集→需求分析→角色定义→思维导图→任务规划→工具选择→代码规范→执行验证→质量检查→收尾总结的完整流程", "用人话说明，通俗易懂"], "constraints": ["不重复定义AI已有的基础能力", "避免冗余的角色描述和规则定义", "保持提示词简洁高效", "确保流程的可操作性和实用性"], "success_criteria": ["提示词结构清晰，逻辑流畅", "Token使用效率显著提升", "AI工作流程更加符合实际思考模式", "用户反馈满意，实用性强"]}, "architecture": {"modules": ["信息收集模块", "需求分析模块", "角色定义模块", "CogniGraph绘制模块", "任务规划模块", "工具选择模块", "代码规范模块", "执行验证模块", "质量检查模块", "收尾总结模块"], "dependencies": ["信息收集 → 需求分析", "需求分析 → 角色定义", "角色定义 → CogniGraph绘制", "CogniGraph绘制 → 任务规划", "任务规划 → 工具选择", "工具选择 → 代码规范", "代码规范 → 执行验证", "执行验证 → 质量检查", "质量检查 → 收尾总结"], "data_flow": ["用户需求 → 信息收集 → 需求分析结果", "需求分析结果 → 角色定义 → 专业角色", "专业角色 → CogniGraph → 项目全貌", "项目全貌 → 任务规划 → 执行计划", "执行计划 → 工具选择 → 工具配置", "工具配置 → 代码规范 → 开发标准", "开发标准 → 执行验证 → 质量结果", "质量结果 → 收尾总结 → 项目交付"]}, "tasks": {"high_priority": ["重新设计提示词整体结构", "引入CogniGraph™概念和文件格式", "建立十步工作流程", "优化信息收集和需求分析流程"], "medium_priority": ["完善工具选择和使用规范", "建立代码规范和质量标准", "设计异常处理机制", "优化输出规范和说明方式"], "low_priority": ["添加更多示例和对比说明", "完善文档结构和格式", "优化用词和表达方式"]}, "decisions": {"key_decisions": ["采用CogniGraph™作为核心记忆机制，替代传统的记忆功能", "按照AI真实思考流程重新排序：信息收集优先于需求分析", "不重复定义AI已有能力，专注于任务规则和思考方式", "引入Sequential thinking工具处理关键决策点", "用人话说明，避免过于专业的表达", "移除Memories功能：CogniGraph+README已足够，避免信息分散和维护负担"], "mcp_analysis": ["利用内置能力策略：避免重复定义AI已有能力，Token效率最大化，避免指令冲突，充分利用工具原生能力", "CogniGraph上下文引擎：结构化存储+高度压缩+可视化友好+状态持久化+动态更新，将AI从健忘症患者变成有完整记忆的专家", "按需MCP深度思考：宏观用CogniGraph管理，微观用Sequential thinking深度分析，职责分离+效率优化+质量保证+可追溯性", "信息收集前置流程：符合AI认知模式，确保信息充分性+逻辑严密性+可控性+可重复性，从随机聊天升级为工程化协作"]}, "progress": {"completed": ["分析用户与DeepSeek R1的讨论内容", "理解CogniGraph™概念和优势", "设计新的十步工作流程", "创建v0.002版本的提示词系统", "建立项目的CogniGraph文件", "移除Memories功能，简化文件管理"], "in_progress": ["等待用户反馈和确认", "准备进一步优化和调整"], "pending": ["根据用户反馈进行调整", "创建使用示例和测试案例", "编写详细的使用说明文档"]}, "insights": {"key_innovations": ["CogniGraph™作为AI外部大脑的概念", "信息收集前置的工作流程设计", "按需调用Sequential thinking的深度分析机制", "利用内置能力避免冗余定义的策略"], "best_practices": ["多源信息收集和交叉验证", "复杂度判断机制自动触发CogniGraph", "任务原子化和状态管理", "质量检查和异常处理机制"], "lessons_learned": ["思维导图比传统记忆更高效", "结构化思考工具能显著提升决策质量", "用人话说明比专业术语更有效", "工程化流程比聊天式开发更可靠", "双文件核心（CogniGraph+README）比多文件管理更专注高效"]}}