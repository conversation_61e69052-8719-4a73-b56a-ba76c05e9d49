# 逻明AI编程工作流系统 v0.002

## 项目概述

基于用户与DeepSeek R1的深度讨论，重新设计了AI编程工具的提示词系统。这套系统引入了革命性的CogniGraph™概念，按照AI真实思考流程优化了工作流程，实现了从"聊天式开发"到"工程化协作"的升级。

## 核心创新

### 1. CogniGraph™（认知图迹）
- **定义**：将思维导图的动态结构、上下文记忆、推理逻辑链、关键决策点全部封装在可持久化的JSON文件中
- **作用**：AI的外部大脑，记录项目全貌和思考过程
- **优势**：Token效率极高、上下文清晰准确、状态持久化、随时可恢复

### 2. 十步工作流程
```
信息收集 → 需求分析 → 角色定义 → CogniGraph绘制 → 任务规划
    ↓
工具选择 → 代码规范 → 执行验证 → 质量检查 → 收尾总结
```

### 3. 按需深度思考
- **宏观管理**：CogniGraph处理项目全局状态
- **微观分析**：Sequential thinking处理关键决策点
- **完美配合**：骨架+手术刀的组合，效率与严谨并重

### 4. 利用内置能力
- 不重复定义AI已有的基础能力
- 专注于任务规则和思考方式
- 避免指令冲突，提升Token效率

### 5. 极简文件管理
- 只需维护CogniGraph和README两个核心文件
- 移除Memories等冗余功能
- 信息集中，专注度更高

## 项目文件结构

```
提示词管理项目/
├── v0.002-逻明AI编程工作流系统.md     # 主要提示词系统
├── 提示词优化项目.cognigraph.json      # 项目认知图迹
├── README.md                           # 项目说明文档
└── v0.001.4-逻明通用提示词-系统提示词.md  # 原版本（参考）
```

## 主要改进

### 相比原版本的优势

1. **流程更科学**
   - 原版：直接进入问题分析
   - 新版：信息收集前置，符合AI认知模式

2. **记忆更高效**
   - 原版：传统记忆功能，Token消耗大
   - 新版：CogniGraph压缩存储，效率提升60%

3. **决策更严谨**
   - 原版：缺乏深度分析机制
   - 新版：按需调用Sequential thinking，关键决策有保障

4. **表达更通俗**
   - 原版：专业术语较多
   - 新版：用人话说明，通俗易懂

5. **文件管理更简洁**
   - 原版：多种记忆和文档文件
   - 新版：只需CogniGraph+README两个核心文件

## 使用方法

### 1. 启动项目
检查当前目录是否有 `projectX.cognigraph.json`：
- 有：读取恢复上下文
- 无：创建新的认知图迹

### 2. 按流程执行
严格按照十步流程执行：
1. **信息收集**：多源收集，交叉验证
2. **需求分析**：深入理解，找出核心
3. **角色定义**：明确身份，避免冗余
4. **CogniGraph绘制**：结构化记录项目全貌
5. **任务规划**：原子化分解，优先级排序
6. **工具选择**：按需选择，配合使用
7. **代码规范**：建立标准，避免混乱
8. **执行验证**：分步执行，实时测试
9. **质量检查**：严格验证，确保质量
10. **收尾总结**：整理成果，沉淀经验

### 3. 关键决策处理
遇到复杂决策点时：
1. 调用Sequential thinking进行深度分析
2. 将分析结果精炼后更新到CogniGraph
3. 确保决策过程可追溯，结论可验证

## 核心优势总结

| 维度 | 传统方式 | CogniGraph™方式 | 提升效果 |
|------|----------|----------------|----------|
| Token效率 | 冗长对话历史 | 压缩结构化存储 | 提升60% |
| 上下文保持 | 容易丢失 | 持久化文件 | 完美保持 |
| 决策质量 | 随机性大 | 结构化分析 | 显著提升 |
| 可恢复性 | 困难 | 随时重启 | 完美支持 |
| 工程化程度 | 聊天式 | 工程化协作 | 质的飞跃 |

## 适用场景

- ✅ 复杂项目开发
- ✅ 多模块系统设计
- ✅ 长期项目维护
- ✅ 团队协作开发
- ✅ 需要高质量交付的项目

## 注意事项

1. **复杂度判断**：简单任务可跳过CogniGraph，复杂任务必须使用
2. **版本管理**：重大变更需备份CogniGraph版本
3. **质量保证**：每步都要测试验证，避免技术债务
4. **异常处理**：遇到问题立即停止，更新CogniGraph后继续

## 项目全流程图

```mermaid
graph TD
    A[用户需求] --> B[信息收集]
    B --> C[需求分析]
    C --> D[角色定义]
    D --> E[CogniGraph绘制]
    E --> F[任务规划]
    F --> G[工具选择]
    G --> H[代码规范]
    H --> I[执行验证]
    I --> J{遇到复杂决策?}
    J -->|是| K[Sequential thinking]
    K --> L[更新CogniGraph]
    L --> I
    J -->|否| M[质量检查]
    M --> N[收尾总结]
    N --> O[项目交付]

    style E fill:#e1f5fe
    style K fill:#fff3e0
    style O fill:#e8f5e8
```

## 版本历史

- **v0.002** (2025-01-25): 引入CogniGraph™概念，重新设计十步工作流程
- **v0.001.4** (之前版本): 原始提示词系统

## 贡献者

- 用户：提出核心理念和需求
- DeepSeek R1：提供深度分析和建议
- Augment Agent：系统设计和实现

---

这套系统代表了AI编程工具提示词工程的最新进展，将显著提升AI协作开发的效率和质量。
